/* Loading spinner styles */
.spinner-border {
  width: 2rem;
  height: 2rem;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Table hover effects */
.table-hover tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

/* Button group spacing */
.btn-group .btn {
  margin: 0 1px;
}

/* Modal enhancements */
.modal-lg {
  max-width: 800px;
}

/* View mode styling */
.form-control-plaintext {
  padding-left: 0;
  padding-right: 0;
  border: none;
  background-color: transparent;
  word-wrap: break-word;
}

/* Logo preview styling */
.img-fluid {
  max-width: 100%;
  height: auto;
}

/* Empty state styling */
.text-center .display-4 {
  color: #6c757d;
  opacity: 0.5;
}

/* Action buttons styling */
.btn-group .btn:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease;
}

/* Loading overlay for buttons */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Toast notification positioning */
.toast-container {
  z-index: 1055;
}

/* Responsive table improvements */
@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.875rem;
  }

  .btn-group .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .modal-lg {
    max-width: 95%;
  }
}

/* Form validation styling */
.is-invalid {
  border-color: #dc3545;
}

.text-danger {
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Success/Error states */
.alert {
  border-radius: 0.375rem;
  border: none;
}

.alert-success {
  background-color: #d1edff;
  color: #0c5460;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
}