<div class="body-background-overlay" [class.scrolled]="isScrolled">
  <nav class="enhanced-navbar">
    <div class="nav-container">
      <!-- Logo Section -->
      <div class="logo-section">
        <div class="logo-wrapper">
          <div class="logo-glow"></div>
          <img
            src="https://insizon-email-templates-bucket.s3.us-east-2.amazonaws.com/insizon/insizon-logo-v2-png.png"
            alt="Insizon Logo"
            class="logo-img"
          />
          <!-- <div class="logo-text">
            <span class="brand-name">Insizon</span>
            <span class="brand-tagline">Innovation Simplified</span>
          </div> -->
        </div>
      </div>

      <!-- Desktop Navigation -->
      <div class="nav-links-desktop">
        <ul class="nav-list">
          <li class="nav-item">
            <a href="/home/<USER>" class="nav-link">
              <!-- <span class="nav-icon">📦</span> -->
              <span>Products</span>
              <div class="nav-underline"></div>
            </a>
          </li>
          <li class="nav-item">
            <a href="/home/<USER>" class="nav-link">
              <!-- <span class="nav-icon">ℹ️</span> -->
              <span>About</span>
              <div class="nav-underline"></div>
            </a>
          </li>
          <li class="nav-item">
            <a href="/home/<USER>" class="nav-link">
              <!-- <span class="nav-icon">💼</span> -->
              <span>Careers</span>
              <div class="nav-underline"></div>
            </a>
          </li>
          <li class="nav-item">
            <a href="/home/<USER>" class="nav-link">
              <!-- <span class="nav-icon">📞</span> -->
              <span>Contact</span>
              <div class="nav-underline"></div>
            </a>
          </li>
          <li class="nav-item">
            <a href="/home/<USER>" class="nav-link">
              <!-- <span class="nav-icon">🐛</span> -->
              <span>Bug-Logs</span>
              <div class="nav-underline"></div>
            </a>
          </li>
            <li class="nav-item">
            <a href="/home/<USER>" class="nav-link">
              <!-- <span class="nav-icon">🚀</span> -->
              <span>Faq</span>
              <div class="nav-underline"></div>
            </a>
          </li>
          <!-- Show Login/Register when NOT logged in -->
          <ng-container *ngIf="!(currentUser$ | async)">
            <li class="nav-item cta-item">
              <a routerLink="/home/<USER>" class="nav-link cta-link">
                <span class="nav-icon">🚀</span>
                <span>Register</span>
                <div class="cta-glow"></div>
              </a>
            </li>
            <li class="nav-item cta-item">
              <a routerLink="/login" class="nav-link cta-link">
                <span class="nav-icon">🔑</span>
                <span>Login</span>
                <div class="cta-glow"></div>
              </a>
            </li>
          </ng-container>

          <!-- Show User Info when logged in -->
          <ng-container *ngIf="currentUser$ | async as user">
            <li class="nav-item user-info">
              <div class="user-dropdown">
                <button class="user-button" (click)="navigateToProfile()">
                  <span class="user-icon">👤</span>
                  <span class="user-name">{{ user.firstName }}</span>
                  <span class="user-role" *ngIf="user.role">({{ user.role }})</span>
                </button>
              </div>
            </li>
            <li class="nav-item cta-item">
              <button (click)="logout()" class="nav-link cta-link logout-btn">
                <span class="nav-icon">🚪</span>
                <span>Logout</span>
                <div class="cta-glow"></div>
              </button>
            </li>
          </ng-container>
        </ul>
      </div>

      <!-- Mobile Menu Button -->
      <div class="mobile-menu-button" (click)="toggleMobileMenu()">
        <div class="hamburger" [class.active]="isMobileMenuOpen">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </div>

    <!-- Mobile Menu -->
    <div class="mobile-menu" [class.active]="isMobileMenuOpen">
      <ul class="mobile-nav-list">
        <li><a href="/home/<USER>" class="mobile-nav-link">Products</a></li>
        <li><a href="/home/<USER>" class="mobile-nav-link">About</a></li>
        <li><a href="/home/<USER>" class="mobile-nav-link">Careers</a></li>
        <li><a href="/home/<USER>" class="mobile-nav-link">Contact</a></li>
        <li><a href="/home/<USER>" class="mobile-nav-link">Bug-Logs</a></li>
        <li><a href="/home/<USER>" class="mobile-nav-link">FAQ</a></li>

        <!-- Mobile Auth Section -->
        <div class="mobile-auth-section">
          <!-- Show Login/Register when NOT logged in -->
          <ng-container *ngIf="!(currentUser$ | async)">
            <li><a routerLink="/home/<USER>" class="mobile-nav-link mobile-cta">Register</a></li>
            <li><a routerLink="/login" class="mobile-nav-link mobile-cta">Login</a></li>
          </ng-container>

          <!-- Show User Info when logged in -->
          <ng-container *ngIf="currentUser$ | async as user">
            <li class="mobile-user-info">
              <div class="mobile-user-details">
                <span class="mobile-user-name">👤 {{ user.firstName }}</span>
                <span class="mobile-user-role" *ngIf="user.role">({{ user.role }})</span>
              </div>
            </li>
            <li><button (click)="logout()" class="mobile-nav-link mobile-logout">🚪 Logout</button></li>
          </ng-container>
        </div>
      </ul>
    </div>

  </nav>

  <!-- Floating particles for navbar -->
  <div class="nav-particles">
    <div class="particle nav-particle-1"></div>
    <div class="particle nav-particle-2"></div>
    <div class="particle nav-particle-3"></div>
  </div>
</div>
