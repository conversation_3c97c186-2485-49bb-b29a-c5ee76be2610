<div class="container mt-4">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2>App Management</h2>
    <button class="btn btn-primary" (click)="openAddAppModal()">
      <i class="bi bi-plus-circle"></i> Add App
    </button>
  </div>

  <div *ngIf="isLoading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-2 text-muted">Loading apps...</p>
  </div>

  <div *ngIf="!isLoading" class="table-responsive" style="max-height: 400px; overflow-y: auto;">
    <table class="table table-bordered table-hover">
      <thead class="table-light">
        <tr>
          <th>#</th>
          <th>Title</th>
          <th>Description</th>
          <th>Website</th>
          <th>Store</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let app of apps; let i = index">
          <td>{{ (pageNumber - 1) * pageSize + i + 1 }}</td>
          <td>{{ app.title }}</td>
          <td>{{ app.description | slice:0:50 }}{{ app.description?.length > 50 ? '...' : '' }}</td>
          <td>
            <a *ngIf="app.websiteUrl" [href]="app.websiteUrl" target="_blank" class="text-decoration-none">
              <i class="bi bi-globe"></i> Website
            </a>
            <span *ngIf="!app.websiteUrl" class="text-muted">-</span>
          </td>
          <td>
            <a *ngIf="app.storeUrl" [href]="app.storeUrl" target="_blank" class="text-decoration-none">
              <i class="bi bi-shop"></i> Store
            </a>
            <span *ngIf="!app.storeUrl" class="text-muted">-</span>
          </td>
          <td>
            <div class="btn-group" role="group">
              <button class="btn btn-sm btn-warning" (click)="openEditAppModal(app)" title="Edit App">
                <i class="bi bi-pencil-square"></i>
              </button>
              <button
                class="btn btn-sm btn-danger"
                (click)="openDeleteConfirmModal(app)"
                [disabled]="isDeleting"
                title="Delete App">
                <i class="bi bi-trash"></i>
              </button>
            </div>
          </td>
        </tr>
        <tr *ngIf="apps.length === 0 && !isLoading">
          <td colspan="6" class="text-center text-muted py-4">
            <i class="bi bi-inbox display-4 d-block mb-2"></i>
            No apps found.
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <div *ngIf="totalCount > 0" class="d-flex justify-content-between align-items-center mt-3">
    <div class="text-muted">
      Showing
      {{ (pageNumber - 1) * pageSize + 1 }}
      –
      {{
        (pageNumber * pageSize) > totalCount
          ? totalCount
          : (pageNumber * pageSize)
      }}
      of {{ totalCount }} apps
    </div>

    <nav *ngIf="totalPages > 1" aria-label="App list pagination">
      <ul class="pagination pagination-sm mb-0">
        <li class="page-item" [class.disabled]="pageNumber === 1">
          <button
            class="page-link"
            (click)="loadApps(pageNumber - 1)"
            [disabled]="pageNumber === 1"
          >
            Previous
          </button>
        </li>
        <li
          class="page-item"
          *ngFor="let page of totalPagesArray()"
          [class.active]="page === pageNumber"
        >
          <button class="page-link" (click)="loadApps(page)">
            {{ page }}
          </button>
        </li>
        <li class="page-item" [class.disabled]="pageNumber === totalPages">
          <button
            class="page-link"
            (click)="loadApps(pageNumber + 1)"
            [disabled]="pageNumber === totalPages"
          >
            Next
          </button>
        </li>
      </ul>
    </nav>
  </div>

  <div class="modal fade" id="appModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <form (ngSubmit)="saveApp()" #appForm="ngForm">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="bi bi-pencil-square me-2" *ngIf="isEditMode"></i>
              <i class="bi bi-plus-circle me-2" *ngIf="!isEditMode"></i>
              {{ isEditMode ? 'Edit App' : 'Add New App' }}
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <div class="row">
              <div class="col-12">
                <div class="mb-3">
                  <label for="title" class="form-label">Title *</label>
                  <input
                    type="text"
                    id="title"
                    class="form-control"
                    [(ngModel)]="currentApp.title"
                    name="title"
                    required
                    [disabled]="isSubmitting"
                  />
                  <div *ngIf="appForm.submitted && !currentApp.title" class="text-danger">
                    Title is required.
                  </div>
                </div>
                <div class="mb-3">
                  <label class="form-label">Description</label>
                  <textarea
                    class="form-control"
                    [(ngModel)]="currentApp.description"
                    name="description"
                    rows="3"
                    [disabled]="isSubmitting"
                    placeholder="Enter app description..."
                  ></textarea>
                </div>
                <div class="mb-3">
                  <label class="form-label">Logo URL</label>
                  <input
                    type="url"
                    class="form-control"
                    [(ngModel)]="currentApp.logoUrl"
                    name="logoUrl"
                    [disabled]="isSubmitting"
                    placeholder="https://example.com/logo.png"
                  />
                </div>
                <div class="mb-3">
                  <label class="form-label">Website URL</label>
                  <input
                    type="url"
                    class="form-control"
                    [(ngModel)]="currentApp.websiteUrl"
                    name="websiteUrl"
                    [disabled]="isSubmitting"
                    placeholder="https://example.com"
                  />
                </div>
                <div class="mb-3">
                  <label class="form-label">Store URL</label>
                  <input
                    type="url"
                    class="form-control"
                    [(ngModel)]="currentApp.storeUrl"
                    name="storeUrl"
                    [disabled]="isSubmitting"
                    placeholder="https://play.google.com/store/apps/details?id=..."
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" [disabled]="isSubmitting">
              <i class="bi bi-x-circle me-1"></i>Cancel
            </button>
            <button
              type="submit"
              class="btn btn-primary"
              [disabled]="!appForm.valid || isSubmitting">
              <span *ngIf="isSubmitting" class="spinner-border spinner-border-sm me-2" role="status"></span>
              <i *ngIf="!isSubmitting && isEditMode" class="bi bi-check-circle me-1"></i>
              <i *ngIf="!isSubmitting && !isEditMode" class="bi bi-plus-circle me-1"></i>
              {{ isSubmitting ? 'Saving...' : (isEditMode ? 'Update App' : 'Add App') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header border-0 pb-0">
          <h5 class="modal-title text-danger">
            <i class="bi bi-exclamation-triangle me-2"></i>
            Confirm Delete
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body pt-0">
          <div class="text-center mb-3">
            <div class="bg-danger bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
              <i class="bi bi-trash text-danger" style="font-size: 2rem;"></i>
            </div>
          </div>
          <p class="text-center mb-3">
            Are you sure you want to delete the app
            <strong>"{{ appToDelete?.title }}"</strong>?
          </p>
          <p class="text-center text-muted small mb-0">
            This action cannot be undone.
          </p>
        </div>
        <div class="modal-footer border-0 pt-0">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" [disabled]="isDeleting">
            <i class="bi bi-x-circle me-1"></i>Cancel
          </button>
          <button type="button" class="btn btn-danger" (click)="confirmDelete()" [disabled]="isDeleting">
            <span *ngIf="isDeleting" class="spinner-border spinner-border-sm me-2" role="status"></span>
            <i *ngIf="!isDeleting" class="bi bi-trash me-1"></i>
            {{ isDeleting ? 'Deleting...' : 'Yes, Delete' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
