<nav class="navbar navbar-expand-lg navbar-light bg-light">
  <div class="container-fluid">
    <button class="btn btn-outline-dark" (click)="toggleSidebar.emit()">
      ☰
    </button>
    <span class="navbar-brand">{{pageTitle}}</span>

    <!-- Admin User Info -->
    <div class="navbar-nav ms-auto" *ngIf="currentUser$ | async as user">
      <div class="nav-item dropdown">
        <div class="admin-user-info">
          <span class="admin-user-name">👤 {{ user.firstName }}</span>
          <span class="admin-user-role" *ngIf="user.role">({{ user.role }})</span>
          <button class="btn btn-outline-danger btn-sm ms-2" (click)="logout()">
            🚪 Logout
          </button>
        </div>
      </div>
    </div>
  </div>
</nav>
