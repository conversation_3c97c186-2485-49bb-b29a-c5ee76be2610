.applied-jobs-container {
  padding: 2rem;
  background-color: #f9f9f9;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  max-width: 1200px;
  margin: auto;
}

.section-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-align: center;
  color: #333;
}

.error-message {
  color: #d32f2f;
  text-align: center;
  font-weight: 500;
  margin-bottom: 1rem;
}

.table-wrapper {
  overflow-x: auto;
}

.applied-jobs-table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.applied-jobs-table th, .applied-jobs-table td {
  padding: 0.75rem 1rem;
  text-align: left;
}

.applied-jobs-table th {
  background-color: #1976d2;
  color: #fff;
  font-weight: 600;
}

.applied-jobs-table tbody tr:nth-child(even) {
  background-color: #f1f1f1;
}

.applied-jobs-table tbody tr:hover {
  background-color: #e3f2fd;
}

.view-resume-link {
  color: #1976d2;
  text-decoration: none;
  font-weight: 500;
}

.view-resume-link:hover {
  text-decoration: underline;
}

.no-data {
  text-align: center;
  font-size: 1.2rem;
  color: #888;
  margin-top: 2rem;
}

/* Enhanced Pagination Styles */
.pagination-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 2rem 0;
  gap: 1rem;
}

.pagination-nav {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  padding: 1rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.pagination-btn {
  padding: 0.75rem 1rem;
  border: 1px solid #e0e0e0;
  background: white;
  color: #333;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

.pagination-btn:hover:not(:disabled) {
  background: #1976d2;
  color: white;
  border-color: #1976d2;
  transform: translateY(-2px);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f5f5f5;
}

.pagination-btn.active {
  background: #1976d2;
  color: white;
  border-color: #1976d2;
}

.pagination-numbers {
  display: flex;
  gap: 0.25rem;
}

.page-btn {
  min-width: 40px;
  padding: 0.5rem;
}

.prev-btn, .next-btn {
  padding: 0.75rem 1.25rem;
  font-weight: 600;
}

.pagination-info {
  color: #666;
  font-size: 0.9rem;
  text-align: center;
  margin-top: 0.5rem;
}

/* Responsive pagination */
@media (max-width: 768px) {
  .pagination-nav {
    padding: 0.75rem;
    gap: 0.25rem;
  }

  .pagination-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }

  .prev-btn, .next-btn {
    padding: 0.5rem 1rem;
  }

  .pagination-info {
    font-size: 0.8rem;
  }
}
