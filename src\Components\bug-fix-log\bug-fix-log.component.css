:root {
  --gold-color: #ffd700;
  --purple-color: #8a2be2;
}

.bug-log-page {
  min-height: 100vh;
}

/* Hero Section */
.bug-hero {
  position: relative;
  min-height: 50vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    var(--gold-color) 0%,
    rgba(255, 215, 0, 0.8) 25%,
    rgba(87, 87, 87, 0.9) 75%,
    rgba(0, 0, 0, 0.95) 100%
  );
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 215, 0, 0.1);
  animation: float 8s ease-in-out infinite;
}

.circle-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.circle-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 20%;
  animation-delay: 3s;
}

.circle-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 70%;
  animation-delay: 6s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(180deg);
  }
}

.hero-content {
  text-align: center;
  z-index: 2;
  max-width: 800px;
  padding: 80px 0;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.5);
  border-radius: 30px;
  padding: 12px 24px;
  margin-bottom: 1rem;
  color: white;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.badge-icon {
  font-size: 1.2rem;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  color: white;
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.title-word {
  display: inline-block;
  animation: titleSlide 1s ease-out forwards;
  opacity: 0;
  transform: translateY(50px);
}


@keyframes titleSlide {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

/* Main Content */
.bug-content {
  padding: 4rem 0;
  background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Action Header */
.action-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.header-info {
  flex: 1;
}

.section-title {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.section-subtitle {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 1rem;
}

.action-btn {
  display: flex;
  align-items: start;
  gap: 0.75rem;
  padding: 9px 20px;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.create-btn {
  border: 1px solid var(--gold-color);
  background-color: transparent;
  color: var(--gold-color);

.create-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.6);
}
}

.request-btn {
  background: linear-gradient(45deg, var(--purple-color), var(--gold-color));
  color: white;
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.4);
}
.request-btn .btn-icon {
    font-size: 21px;
    line-height: normal;
}
.request-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(138, 43, 226, 0.6);
}

.btn-icon {
  font-size: 33px;
    line-height: 18px;
}

/* Statistics Section */
.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  font-size: 2rem;
  padding: 0.75rem;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.25rem;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
}

/* Bug List Section */
.bug-list-section {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.list-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.list-count {
  color: #666;
  font-size: 0.9rem;
  background: #f8f9fa;
  padding: 0.5rem 1rem;
  border-radius: 20px;
}

/* Bug Grid */
.bug-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.bug-card {
  background: #fafafa;
  border-radius: 15px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.bug-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  transition: width 0.3s ease;
}

.bug-card[data-status="fixed"]::before {
  background: #28a745;
}

.bug-card[data-status="in-progress"]::before {
  background: #ffc107;
}

.bug-card[data-status="open"]::before {
  background: #dc3545;
}

.bug-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
  background: white;
}

.bug-card:hover::before {
  width: 8px;
}

.bug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.bug-id {
  background: linear-gradient(45deg, var(--gold-color), var(--purple-color));
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: bold;
  color: white;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-fixed {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-in-progress {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-open {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.status-icon {
  font-size: 1rem;
}

.bug-content {
  margin-bottom: 1.5rem;
}

.bug-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
}

.bug-description {
  color: #666;
  line-height: 1.6;
  margin: 0;
  font-size: 0.95rem;
}

.bug-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #e0e0e0;
}

.bug-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  font-size: 0.85rem;
}

.date-icon {
  font-size: 1rem;
}

.bug-priority {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #666;
}

.priority-dot {
  width: 8px;
  height: 8px;
  background: #ffc107;
  border-radius: 50%;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.empty-state p {
  margin-bottom: 2rem;
}

.empty-action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(45deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.empty-action-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.6);
}

/* Dialog Styles */
::ng-deep .custom-dialog .p-dialog-header {
  background: linear-gradient(45deg, var(--gold-color), var(--purple-color));
  color: white;
  border-radius: 15px 15px 0 0;
  padding: 1.5rem;
  font-weight: 600;
}

::ng-deep .custom-dialog .p-dialog-content {
  padding: 0;
  border-radius: 0 0 15px 15px;
}

::ng-deep .custom-dialog .p-dialog {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.dialog-content {
  padding: 2rem;
}

.dialog-icon {
  text-align: center;
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

/* Form Styles */
.bug-form,
.fix-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.label-icon {
  font-size: 1rem;
}

.optional-label {
  color: #999;
  font-weight: normal;
  font-size: 0.8rem;
  margin-left: 0.5rem;
}

.form-input,
.form-textarea {
  padding: 0.75rem 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--gold-color);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

/* Custom Dropdown */
::ng-deep .custom-dropdown .p-dropdown {
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  transition: all 0.3s ease;
}

::ng-deep .custom-dropdown .p-dropdown:not(.p-disabled).p-focus {
  border-color: var(--gold-color);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
  padding-top: 1.5rem;
  border-top: 1px solid #f0f0f0;
}

.form-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.cancel-btn {
  background: transparent;
  border: 2px solid #ddd;
  color: #666;
}

.cancel-btn:hover {
  border-color: #999;
  color: #333;
}

.submit-btn {
  background: linear-gradient(45deg, var(--purple-color), #9932cc);
  color: white;
  box-shadow: 0 4px 15px rgba(138, 43, 226, 0.4);
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(138, 43, 226, 0.6);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .bug-grid {
    grid-template-columns: 1fr;
  }

  .stats-section {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .action-header {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .action-btn {
    justify-content: center;
  }

  .stats-section {
    grid-template-columns: 1fr;
  }

  .list-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .bug-footer {
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-start;
  }

  .form-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .content-container {
    padding: 0 1rem;
  }

  .bug-list-section {
    padding: 1.5rem;
  }

  .hero-content {
    padding: 1rem;
  }

  .dialog-content {
    padding: 1.5rem;
  }

  .bug-card {
    padding: 1rem;
  }
}

/* Loading States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--purple-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.loading-container p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  margin: 3rem 0;
}

.pagination-nav {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: white;
  padding: 1rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.pagination-btn {
  padding: 0.75rem 1rem;
  border: 1px solid #e0e0e0;
  background: white;
  color: #333;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-btn:hover:not(:disabled) {
  background: var(--purple-color);
  color: white;
  border-color: var(--purple-color);
  transform: translateY(-2px);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f5f5f5;
}

.pagination-btn.active {
  background: var(--purple-color);
  color: white;
  border-color: var(--purple-color);
}

.pagination-numbers {
  display: flex;
  gap: 0.25rem;
}

.page-btn {
  min-width: 40px;
  padding: 0.5rem;
}

.prev-btn, .next-btn {
  padding: 0.75rem 1.25rem;
  font-weight: 600;
}

.pagination-info {
  color: #666;
  font-size: 0.9rem;
  margin-left: auto;
}

/* Enhanced list header */
.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.list-count {
  color: #666;
  font-size: 0.9rem;
}

/* Responsive pagination */
@media (max-width: 768px) {
  .pagination-nav {
    padding: 0.75rem;
    gap: 0.25rem;
  }

  .pagination-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }

  .prev-btn, .next-btn {
    padding: 0.5rem 1rem;
  }

  .pagination-info {
    display: none;
  }

  .list-header {
    flex-direction: column;
    align-items: flex-start;
  }
}
