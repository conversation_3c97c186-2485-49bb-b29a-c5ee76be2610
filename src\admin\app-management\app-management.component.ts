import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { AppManagementService } from '../../services/app-management.service';
import { NgFor, NgIf, SlicePipe } from '@angular/common';
import { PageTitleService } from '../../Utils/_services/page-title.service';
import { ToastrService, ToastrModule } from 'ngx-toastr';

declare var bootstrap: any;

@Component({
  selector: 'app-app-management',
  standalone: true,
  imports: [FormsModule, NgFor, NgIf, SlicePipe, ToastrModule],
  templateUrl: './app-management.component.html',
  styleUrl: './app-management.component.css'
})
export class AppManagementComponent {
  apps: any[] = [];
  pageNumber = 1;
  pageSize = 5;
  totalCount = 0;
  totalPages = 0;

  isLoading = false;
  isSubmitting = false;
  isDeleting = false;

  isEditMode = false;

  currentApp: any = {
    id: null,
    title: '',
    description: '',
    logoUrl: '',
    websiteUrl: '',
    storeUrl: ''
  };

  constructor(
    private appService: AppManagementService,
    private pageTitle: PageTitleService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.pageTitle.setTitle('App Management');
    this.loadApps(this.pageNumber);
  }

  loadApps(page: number): void {
    this.pageNumber = page;
    this.isLoading = true;
    const params = { pageNumber: this.pageNumber, pageSize: this.pageSize };

    this.appService.getApps(params).subscribe({
      next: (response) => {
        this.apps = response.items;
        this.totalCount = response.totalCount;
        this.totalPages = this.totalCount && this.pageSize
          ? Math.ceil(this.totalCount / this.pageSize)
          : 1;
        this.isLoading = false;
      },
      error: (err) => {
        this.isLoading = false;
        console.error('Failed to load apps:', err);
        this.toastr.error('Error loading apps. Please try again.', 'Error');
      }
    });
  }

  totalPagesArray(): number[] {
    return Array.from({ length: this.totalPages }, (_, i) => i + 1);
  }

  openAddAppModal(): void {
    this.isEditMode = false;
    this.resetCurrentApp();
    const modal = new bootstrap.Modal(document.getElementById('appModal')!);
    modal.show();
  }

  openEditAppModal(app: any): void {
    this.isEditMode = true;
    this.currentApp = { ...app };
    const modal = new bootstrap.Modal(document.getElementById('appModal')!);
    modal.show();
  }

  saveApp(): void {
    if (this.isEditMode) {
      this.updateApp();
    } else {
      this.addApp();
    }
  }

  addApp(): void {
    this.isSubmitting = true;
    this.appService.createApp(this.currentApp).subscribe({
      next: () => {
        this.isSubmitting = false;
        this.toastr.success('App created successfully!', 'Success');
        this.closeModal();
        this.loadApps(this.pageNumber);
      },
      error: (err) => {
        this.isSubmitting = false;
        console.error('Add app failed:', err);
        this.toastr.error(err.error?.message || 'Failed to add app. Please try again.', 'Error');
      }
    });
  }

  updateApp(): void {
    this.isSubmitting = true;
    this.appService.updateApp(this.currentApp.id, this.currentApp).subscribe({
      next: () => {
        this.isSubmitting = false;
        this.toastr.info('App updated successfully!', 'Success');
        this.closeModal();
        this.loadApps(this.pageNumber);
      },
      error: (err) => {
        this.isSubmitting = false;
        console.error('Update app failed:', err);
        this.toastr.error(err.error?.message || 'Failed to update app. Please try again.', 'Error');
      }
    });
  }

  appToDelete: any = null;

  openDeleteConfirmModal(app: any): void {
    this.appToDelete = app;
    const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal')!);
    modal.show();
  }

  confirmDelete(): void {
    if (this.appToDelete) {
      this.isDeleting = true;
      this.appService.deleteApp(this.appToDelete.id).subscribe({
        next: () => {
          this.isDeleting = false;
          this.toastr.warning('App deleted successfully!', 'Success');
          this.loadApps(this.pageNumber);
          this.closeDeleteModal();
        },
        error: (err) => {
          this.isDeleting = false;
          console.error('Delete app failed:', err);
          this.toastr.error(err.error?.message || 'Failed to delete app. Please try again.', 'Error');
        }
      });
    }
  }

  closeDeleteModal(): void {
    const modalElement = document.getElementById('deleteConfirmModal')!;
    const modalInstance = bootstrap.Modal.getInstance(modalElement);
    modalInstance?.hide();
    this.appToDelete = null;
  }

  closeModal(): void {
    const modalElement = document.getElementById('appModal')!;
    const modalInstance = bootstrap.Modal.getInstance(modalElement);
    modalInstance?.hide();
    this.resetCurrentApp();
  }

  resetCurrentApp(): void {
    this.currentApp = {
      id: null,
      title: '',
      description: '',
      logoUrl: '',
      websiteUrl: '',
      storeUrl: ''
    };
  }
}
