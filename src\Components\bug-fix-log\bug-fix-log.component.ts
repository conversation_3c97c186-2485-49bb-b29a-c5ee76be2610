import { CommonModule, NgFor } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { AppManagementService } from '../../services/app-management.service';
import { BugReportingService } from '../../services/bug-reporting.service';
import { BugReportingEntityDto } from '../../models/bug-report.model';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-bug-fix-log',
  standalone: true,
  imports: [NgFor, CommonModule, CardModule, DialogModule, 
    FormsModule, DropdownModule, ButtonModule],
  templateUrl: './bug-fix-log.component.html',
  styleUrl: './bug-fix-log.component.css'
})
export class BugFixLogComponent {

  showCreateDialog = false;
  showRequestDialog = false;

  bugs: any[] = [];
  paginatedBugs: any[] = [];
  totalBugs: number = 0;
  currentPage: number = 1;
  itemsPerPage: number = 6;
  totalPages: number = 0;
  isLoading: boolean = false;

  apps: any[]= [];

  selectedApp: any = null;
  newBugTitle: string = '';
  newBugDescription: string = '';

  selectedBug: any = null;
  userComment: string = '';

    constructor(private appService: AppManagementService,
      private bugService: BugReportingService,
      private toastr: ToastrService
    ) {}
  

  ngOnInit(): void {
    this.loadApps();
    this.loadBugs();
  }

  loadApps(): void {
  this.appService.getAllApps().subscribe({
    next: (response) => {
      this.apps = response;
      console.log("apps",this.apps)
    },
    error: (err) => {
      console.error('Failed to load all apps:', err);
      this.toastr.error('Error loading apps. Please try again.', 'Error');
    }
  });
 }

  loadBugs(): void {
    this.isLoading = true;
    this.bugService.getAll(this.currentPage, this.itemsPerPage).subscribe({
      next: (response) => {
        this.bugs = response.items || [];
        this.totalBugs = response.totalCount || 0;
        this.totalPages = Math.ceil(this.totalBugs / this.itemsPerPage);
        this.updatePaginatedBugs();
        this.isLoading = false;
        console.log("Bugs loaded:", this.bugs);
      },
      error: (err) => {
        console.error('Failed to load bugs:', err);
        this.toastr.error('Error loading bugs. Please try again.', 'Error');
        this.isLoading = false;
      }
    });
  }

  updatePaginatedBugs(): void {
    // Since we're using server-side pagination, just use the bugs array directly
    this.paginatedBugs = [...this.bugs];
  }

  openCreateDialog() {
    this.showCreateDialog = true;
  }

  openRequestDialog() {
    this.showRequestDialog = true;
  }

  // submitNewBug() {
  //   if (!this.selectedApp || !this.newBugTitle || !this.newBugDescription) return;

  //   const newBug = {
  //     id: this.bugs.length + 1,
  //     app: this.selectedApp.name,
  //     title: this.newBugTitle,
  //     description: this.newBugDescription,
  //     status: 'open',
  //     createdAt: new Date()
  //   };

  //   this.bugs.push(newBug);

  //   // Clear fields
  //   this.selectedApp = null;
  //   this.newBugTitle = '';
  //   this.newBugDescription = '';
  //   this.showCreateDialog = false;
  // }

  submitNewBug() {
    if (!this.selectedApp || !this.newBugTitle || !this.newBugDescription) {
      this.toastr.warning('Please fill in all required fields.', 'Validation Error');
      return;
    }

    console.log("App id",this.selectedApp)
    const newBug: BugReportingEntityDto = {
      appId: this.selectedApp,
      title: this.newBugTitle,
      description: this.newBugDescription,
    };

    console.log("Testing Data",newBug)

    this.bugService.create(newBug).subscribe({
      next: (createdBug) => {
        // Reload bugs to get updated pagination
        this.loadBugs();
        console.log("Bug created:", createdBug);

        // Show success toast
        this.toastr.success('Bug report created successfully!', 'Success');

        // Clear form and close dialog
        this.selectedApp = null;
        this.newBugTitle = '';
        this.newBugDescription = '';
        this.showCreateDialog = false;
      },
      error: (err) => {
        console.error('Failed to create bug:', err);
        this.toastr.error('Error creating bug. Please try again.', 'Error');
      }
    });
  }

  submitFixRequest() {
    if (!this.selectedBug) return;

    alert(
      `Fix request submitted for: ${this.selectedBug.title}\nComment: ${this.userComment || 'No comment'}`
    );

    this.selectedBug = null;
    this.userComment = '';
    this.showRequestDialog = false;
  }

  getTotalBugs(): number {
    return this.bugs.length;
  }

  getFixedBugs(): number {
    return this.bugs.filter(b => b.status === 'fixed').length;
  }

  getInProgressBugs(): number {
    return this.bugs.filter(b => b.status === 'in-progress').length;
  }

  getOpenBugs(): number {
    return this.bugs.filter(b => b.status === 'open').length;
  }

  // Pagination methods
  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadBugs();
    }
  }

  nextPage(): void {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.loadBugs();
    }
  }

  prevPage(): void {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.loadBugs();
    }
  }

  getPaginationArray(): number[] {
    const maxVisible = 5;
    const start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
    const end = Math.min(this.totalPages, start + maxVisible - 1);
    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }
}
