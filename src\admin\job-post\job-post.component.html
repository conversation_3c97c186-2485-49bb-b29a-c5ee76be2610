<div class="container mt-4">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="fw-bold">Job Posts</h2>
    <button
      class="btn btn-primary"
      data-bs-toggle="modal"
      data-bs-target="#jobModal"
      (click)="openModal('create')">
      Create Job
    </button>
  </div>
  
  <div *ngIf="isLoading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-2 text-muted">Loading jobs...</p>
  </div>

  <div *ngIf="errorMessage && !isLoading" class="alert alert-danger" role="alert">
    <i class="bi bi-exclamation-triangle me-2"></i>
    {{ errorMessage }}
    <button class="btn btn-sm btn-outline-danger ms-2" (click)="loadJobs()">
      <i class="bi bi-arrow-clockwise me-1"></i>Retry
    </button>
  </div>

  <div class="mt-4" *ngIf="!isLoading && jobs && jobs.length > 0; else noJobs">
    <div class="d-flex justify-content-between align-items-center mb-3">
      <div class="text-muted">
        Showing {{ getStartIndex() }} to {{ getEndIndex() }} of {{ totalCount }} jobs
      </div>
      <div class="d-flex align-items-center">
        <label class="form-label me-2 mb-0">Jobs per page:</label>
        <select class="form-select form-select-sm" style="width: auto;" [(ngModel)]="pageSize" (change)="onPageSizeChange()">
          <option [value]="10">10</option>
          <option [value]="20">20</option>
          <option [value]="50">50</option>
          <option [value]="100">100</option>
        </select>
      </div>
    </div>

    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
      <div class="col" *ngFor="let job of jobs; let i = index">
        <div class="card shadow-sm h-100 border-0">
          <div class="card-body">
            <h5 class="card-title fw-bold">{{ job.jobTitle }}</h5>
            <p class="card-text mb-2">{{ job.description }}</p>
            <ul class="list-unstyled mb-3">
              <li><strong>Type:</strong> {{ job.jobType }}</li>
              <li><strong>Mode:</strong> {{ job.mode }}</li>
              <li *ngIf="job.location"><strong>Location:</strong> {{ job.location }}</li>
              <li><strong>Salary:</strong> ₹{{ job.salaryPackage }}</li>
              <li *ngIf="job.requirements"><strong>Requirements:</strong> {{ job.requirements }}</li>
            </ul>
            <button
              class="btn btn-outline-primary btn-sm me-2"
              data-bs-toggle="modal"
              data-bs-target="#jobModal"
              (click)="openModal('edit', job)">
              <i class="bi bi-pencil-square"></i> Edit
            </button>
            <button
              class="btn btn-outline-danger btn-sm"
              data-bs-toggle="modal"
              data-bs-target="#deleteConfirmModal"
              (click)="prepareDelete(i, job.jobId, job.jobTitle)">
              Delete
            </button>
          </div>
        </div>
      </div>
    </div>

    <nav aria-label="Jobs pagination" *ngIf="totalPages > 1" class="mt-4">
      <ul class="pagination justify-content-center">
        <li class="page-item" [class.disabled]="pageNumber === 1">
          <button class="page-link" (click)="prevPage()" [disabled]="pageNumber === 1">
            <i class="bi bi-chevron-left"></i> Previous
          </button>
        </li>

        <li class="page-item" *ngFor="let page of getPaginationArray()" [class.active]="page === pageNumber">
          <button class="page-link" (click)="goToPage(page)">{{ page }}</button>
        </li>

        <li class="page-item" [class.disabled]="pageNumber === totalPages">
          <button class="page-link" (click)="nextPage()" [disabled]="pageNumber === totalPages">
            Next <i class="bi bi-chevron-right"></i>
          </button>
        </li>
      </ul>
    </nav>
  </div>

  <ng-template #noJobs>
    <div class="alert alert-info mt-4 text-center" *ngIf="!isLoading">
      <i class="bi bi-info-circle me-2"></i> No jobs posted yet. Click
      <strong>"Create Job"</strong> to add one.
    </div>
  </ng-template>
</div>

<!-- Create/Edit Job Modal -->
<div
  class="modal fade"
  id="jobModal"
  tabindex="-1"
  aria-labelledby="jobModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content shadow">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="jobModalLabel">
          {{ modalMode === 'create' ? 'Create New Job' : 'Edit Job' }}
        </h5>
        <button
          type="button"
          class="btn-close btn-close-white"
          data-bs-dismiss="modal"
          aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form
          (ngSubmit)="modalMode === 'create' ? postJob() : updateJob()"
          class="row g-3"
          #jobForm="ngForm">
          <div class="col-md-6">
            <label class="form-label fw-semibold">Job Title</label>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="job.jobTitle"
              name="jobTitle"
              required />
          </div>

          <div class="col-md-6">
            <label class="form-label fw-semibold">Company</label>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="job.company"
              name="company"
              required />
          </div>

          <div class="col-md-6">
            <label class="form-label fw-semibold">Job Type</label>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="job.jobType"
              name="jobType"
              required />
          </div>

          <div class="col-md-6">
            <label class="form-label fw-semibold">Mode</label>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="job.mode"
              name="mode"
              required />
          </div>

          <div class="col-md-6">
            <label class="form-label fw-semibold">Location</label>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="job.location"
              name="location"
              required />
          </div>

          <div class="col-md-6">
            <label class="form-label fw-semibold">Salary</label>
            <input
              type="number"
              class="form-control"
              [(ngModel)]="job.salaryPackage"
              name="salaryPackage"
              required />
          </div>

          <div class="col-12">
            <label class="form-label fw-semibold">Requirements</label>
            <input
              type="text"
              class="form-control"
              [(ngModel)]="job.requirements"
              name="requirements"
              required />
          </div>

          <div class="col-12">
            <label class="form-label fw-semibold">Description</label>
            <textarea
              class="form-control"
              rows="4"
              [(ngModel)]="job.description"
              name="description"
              required></textarea>
          </div>

          <div class="col-12 text-end">
            <button
              type="submit"
              class="btn btn-success"
              [disabled]="!jobForm.form.valid">
              {{ modalMode === 'create' ? 'Post Job' : 'Update Job' }}
            </button>
            <button
              type="button"
              class="btn btn-secondary ms-2"
              (click)="resetForm()">
              Reset
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-danger text-white">
        <h5 class="modal-title" id="deleteConfirmModalLabel">
          <i class="bi bi-exclamation-triangle me-2"></i>Confirm Delete
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-3">Are you sure you want to delete this job?</p>
        <div class="alert alert-warning" *ngIf="jobToDelete">
          <strong>Job Title:</strong> {{ jobToDelete.title }}
        </div>
        <p class="text-muted small">This action cannot be undone.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <i class="bi bi-x-circle me-1"></i>Cancel
        </button>
        <button type="button" class="btn btn-danger" (click)="confirmDelete()" [disabled]="isDeleting">
          <span *ngIf="isDeleting" class="spinner-border spinner-border-sm me-2" role="status"></span>
          <i *ngIf="!isDeleting" class="bi bi-trash me-1"></i>
          {{ isDeleting ? 'Deleting...' : 'Yes, Delete' }}
        </button>
      </div>
    </div>
  </div>
</div>
