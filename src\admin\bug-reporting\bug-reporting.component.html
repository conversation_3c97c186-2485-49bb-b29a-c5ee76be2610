<div class="container">
  <div class="header-section">
    <h3>Reported Bugs</h3>
    <button class="btn btn-toggle" (click)="toggleView()" [disabled]="isLoading">
      Switch to {{ viewMode === 'card' ? 'Table View' : 'Card View' }}
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-2 text-muted">Loading bugs...</p>
  </div>

  <!-- 🃏 Card View -->
  <div *ngIf="viewMode === 'card' && !isLoading" class="card-container">
    <div class="bug-card" *ngFor="let bug of bugs">
      <div class="card-content">
        <h5 class="card-title">{{ bug.title }}</h5>
        <div class="card-info">
          <p><strong>Date:</strong> {{ bug.createdAt | date:'yyyy-MM-dd' }}</p>
          <div class="badges-container">
            <span class="badge priority-badge" [ngClass]="getPriorityClass(bug.priority || 'Low')">
              {{ bug.priority || 'Low' }}
            </span>
            <span class="badge status-badge" [ngClass]="getStatusClass(bug.status || 'open')">
              {{ bug.status }}
            </span>
          </div>
        </div>
        <button class="btn btn-view" (click)="viewBug(bug)" [disabled]="isSubmitting">
          <i class="bi bi-eye me-1"></i>View Bug
        </button>
      </div>
    </div>

    <!-- Empty State for Card View -->
    <div *ngIf="bugs.length === 0 && !isLoading" class="text-center py-5">
      <i class="bi bi-inbox display-4 d-block mb-2 text-muted"></i>
      <p class="text-muted">No bugs found.</p>
    </div>
  </div>

  <!-- 📋 Table View -->
  <div *ngIf="viewMode === 'table' && !isLoading" class="table-container">
    <!-- Scrollable Table Container -->
    <div class="table-responsive" style="max-height: 500px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem;">
      <table class="table table-bordered table-hover mb-0">
        <thead class="table-light sticky-top">
          <tr>
            <th style="min-width: 60px;">#</th>
            <th style="min-width: 200px;">Title</th>
            <th style="min-width: 150px;">App Name</th>
            <th style="min-width: 100px;">Priority</th>
            <th style="min-width: 120px;">Status</th>
            <th style="min-width: 120px;">Date</th>
            <th style="min-width: 100px;">Action</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let bug of bugs; let i = index">
            <td>{{ (pageNumber - 1) * pageSize + i + 1 }}</td>
            <td>
              <div class="text-truncate" style="max-width: 200px;" [title]="bug.title">
                {{ bug.title }}
              </div>
            </td>
            <td>
              <div class="text-truncate" style="max-width: 150px;" [title]="bug.appManagement?.title || 'N/A'">
                {{ bug.appManagement?.title || 'N/A' }}
              </div>
            </td>
            <td>
              <span class="badge priority-badge" [ngClass]="getPriorityClass(bug.priority || 'Low')">
                {{ bug.priority }}
              </span>
            </td>
            <td>
              <span class="badge status-badge" [ngClass]="getStatusClass(bug.status || 'open')">
                {{ bug.status }}
              </span>
            </td>
            <td>{{ bug.createdAt | date:'yyyy-MM-dd' }}</td>
            <td>
              <button class="btn btn-primary btn-sm" (click)="viewBug(bug)" [disabled]="isSubmitting">
                <i class="bi bi-eye me-1"></i>View
              </button>
            </td>
          </tr>
          <tr *ngIf="bugs.length === 0 && !isLoading">
            <td colspan="7" class="text-center text-muted py-4">
              <i class="bi bi-inbox display-4 d-block mb-2"></i>
              No bugs found.
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Pagination Summary and Controls -->
  <div *ngIf="totalItems > 0 && !isLoading" class="d-flex justify-content-between align-items-center mt-4">
    <!-- Pagination Summary -->
    <div class="text-muted">
      Showing
      {{ (pageNumber - 1) * pageSize + 1 }}
      –
      {{
        (pageNumber * pageSize) > totalItems
          ? totalItems
          : (pageNumber * pageSize)
      }}
      of {{ totalItems }} bugs
    </div>

    <!-- Enhanced Pagination -->
    <nav *ngIf="totalPages > 1" aria-label="Bug list pagination">
      <ul class="pagination pagination-sm mb-0">
        <li class="page-item" [class.disabled]="pageNumber === 1">
          <button
            class="page-link"
            (click)="changePage(pageNumber - 1)"
            [disabled]="pageNumber === 1 || isLoading"
          >
            <i class="bi bi-chevron-left"></i> Previous
          </button>
        </li>

        <!-- First page -->
        <li class="page-item" [class.active]="pageNumber === 1">
          <button class="page-link" (click)="changePage(1)" [disabled]="isLoading">1</button>
        </li>

        <!-- Ellipsis for large page numbers -->
        <li class="page-item disabled" *ngIf="pageNumber > 3 && totalPages > 5">
          <span class="page-link">...</span>
        </li>

        <!-- Pages around current page -->
        <li
          class="page-item"
          *ngFor="let page of getVisiblePages()"
          [class.active]="page === pageNumber"
        >
          <button class="page-link" (click)="changePage(page)" [disabled]="isLoading">
            {{ page }}
          </button>
        </li>

        <!-- Ellipsis for large page numbers -->
        <li class="page-item disabled" *ngIf="pageNumber < totalPages - 2 && totalPages > 5">
          <span class="page-link">...</span>
        </li>

        <!-- Last page -->
        <li class="page-item" [class.active]="pageNumber === totalPages" *ngIf="totalPages > 1">
          <button class="page-link" (click)="changePage(totalPages)" [disabled]="isLoading">{{ totalPages }}</button>
        </li>

        <li class="page-item" [class.disabled]="pageNumber === totalPages">
          <button
            class="page-link"
            (click)="changePage(pageNumber + 1)"
            [disabled]="pageNumber === totalPages || isLoading"
          >
            Next <i class="bi bi-chevron-right"></i>
          </button>
        </li>
      </ul>
    </nav>
  </div>

  <!-- Modal Popup -->
  <div class="modal-overlay" *ngIf="showModal" (click)="closeModal()">
    <div class="modal-content" (click)="$event.stopPropagation()" *ngIf="selectedBug">
      <div class="modal-header">
        <h4>Bug Details</h4>
        <button class="close-btn" (click)="closeModal()">&times;</button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label><strong>Title:</strong></label>
          <input
            type="text"
            [(ngModel)]="selectedBug.title"
            class="form-control"
            [disabled]="isSubmitting">
        </div>
        <div class="form-group">
          <label><strong>Description:</strong></label>
          <textarea
            [(ngModel)]="selectedBug.description"
            class="form-control"
            rows="3"
            [disabled]="isSubmitting"></textarea>
        </div>
        <div class="form-row">
          <div class="form-group">
            <label><strong>Priority:</strong></label>
            <select
              [(ngModel)]="selectedBug.priority"
              class="form-control"
              [disabled]="isSubmitting">
              <option value="High">High</option>
              <option value="Medium">Medium</option>
              <option value="Low">Low</option>
            </select>
          </div>
          <div class="form-group">
            <label><strong>Status:</strong></label>
            <select
              [(ngModel)]="selectedBug.status"
              class="form-control"
              [disabled]="isSubmitting">
              <option value="Open">Open</option>
              <option value="In Progress">In Progress</option>
              <option value="Closed">Closed</option>
            </select>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" (click)="closeModal()" [disabled]="isSubmitting">
          <i class="bi bi-x-circle me-1"></i>Cancel
        </button>
        <button class="btn btn-primary" (click)="updateBug()" [disabled]="isSubmitting">
          <span *ngIf="isSubmitting" class="spinner-border spinner-border-sm me-2" role="status"></span>
          <i *ngIf="!isSubmitting" class="bi bi-check-circle me-1"></i>
          {{ isSubmitting ? 'Updating...' : 'Update Bug' }}
        </button>
      </div>
    </div>
  </div>
</div>
